# Prop-Based Processing Architecture

## 概述

新的 prop-based 处理架构将每个 prop 的所有变化交给一个 PropProcessor 处理，实现了以下目标：

1. **单一 PropProcessor 处理**：每个 prop 的所有下载、resize 和删除操作由一个 PropProcessor 处理
2. **内部并发下载**：单个 prop 内的多个文件可以并发下载，通过 PropConcurrency 控制
3. **内存缓存**：下载和 resize 后的文件先保存在内存中
4. **批量写盘**：一个 prop 的所有文件都处理完后，一次性写入磁盘
5. **简化架构**：移除了复杂的 WorkerPool 架构，直接使用 PropProcessor

## 新增数据结构

### CachedFile
```go
type CachedFile struct {
    Path     string // 文件相对路径
    Data     []byte // 文件数据
    IsPhoto  bool   // 是否为图片文件
}
```

### PropProcessor
```go
type PropProcessor struct {
    downloader   *Downloader
    result       *AnalysisResult
    board        string
    cachedFiles  []CachedFile
    thumbnailImg image.Image
}
```



### PropProcessResult
```go
type PropProcessResult struct {
    PropID        string
    ThumbnailHash int32
    TnChangedNum  int
    Error         error
}
```

## 处理流程

### 1. 接收任务
- `ProcessAnalysisResult` 直接创建 `PropProcessor` 并调用 `Process` 方法

### 2. 单个 Prop 处理 (PropProcessor.Process)
1. **下载和缓存** (`downloadAndCacheFiles`)
   - **固定 Worker 数量**：创建 `PropConcurrency` 个 worker goroutine
   - **任务分发**：通过 channel 将下载任务分发给 workers
   - **智能图片处理**：使用 `gofile.ProcessImageDataWithResize` 对超过 300KB 的图片进行 resize
   - **内存缓存**：将处理后的数据缓存在 `CachedFile` 结构中

2. **生成缩略图** (`generateThumbnail`)
   - 为第一张图片生成缩略图
   - 缩略图数据也缓存在 `CachedFile` 中
   - 需要删除的旧缩略图文件加入删除列表

3. **批量写盘** (`batchWriteFiles`)
   - 将所有缓存的文件一次性写入所有存储路径
   - 确保目录存在
   - 原子性写入操作

4. **处理删除** (`handleDeletions`)
   - 删除不需要的文件
   - 使用现有的删除逻辑

### 3. 结果处理
- 返回 `PropProcessResult` 包含处理结果
- 更新数据库文档

## 配置选项

### DownloaderConfig 简化后的字段
```go
type DownloaderConfig struct {
    PropConcurrency              int `json:"prop_concurrency"`              // Prop 并发数
    MaxRetries                   int `json:"max_retries"`                   // 最大重试次数
    ConsecutiveFailuresThreshold int `json:"consecutive_failures_threshold"` // 连续失败阈值
}
```

### 默认配置
- `PropConcurrency`: 3 (同时处理 3 个 prop)
- `MaxRetries`: 3 (最大重试 3 次)
- `ConsecutiveFailuresThreshold`: 3 (连续失败 3 次后告警)

## 优势

1. **减少磁盘 I/O**：批量写入减少了磁盘操作次数
2. **更好的错误处理**：单个 prop 的所有操作在一个 PropProcessor 中，便于错误追踪
3. **内存优化**：可以在内存中对文件进行预处理
4. **原子性**：一个 prop 的所有文件要么全部成功，要么全部失败
5. **固定并发控制**：使用固定数量的 worker goroutine，避免创建过多 goroutine
6. **智能图片处理**：自动检测图片大小，只对需要的图片进行 resize
7. **复用现有功能**：充分利用 `gofile` 包的下载和图片处理功能
8. **简化架构**：移除了复杂的 WorkerPool 设计，代码更简洁易维护

## 使用方法

### 基本使用
```go
config := NewDefaultConfig()
// config.PropConcurrency = 3 (默认值，可以调整)

downloader, err := NewDownloader(&DownloaderOptions{
    Config: config,
    StoragePaths: []string{"/path/to/storage"},
})
```

### 自定义并发数
```go
config := NewDefaultConfig()
config.PropConcurrency = 5 // 同时处理 5 个 prop

downloader, err := NewDownloader(&DownloaderOptions{
    Config: config,
    StoragePaths: []string{"/path/to/storage"},
})
```

## 测试

运行测试验证新架构：
```bash
go test -v -run TestPropProcessor
```

## 注意事项

1. **内存使用**：新架构会在内存中缓存文件数据，需要注意内存使用量
2. **错误处理**：单个文件下载失败会导致整个 prop 处理失败
3. **并发控制**：通过 `PropConcurrency` 控制同时处理的 prop 数量
4. **架构简化**：移除了旧的 WorkerPool 架构，代码更简洁易维护

## 技术实现细节

### Worker Pool 架构
```go
// 创建固定数量的 worker goroutine
numWorkers := pp.downloader.Config.PropConcurrency
for i := 0; i < numWorkers; i++ {
    wg.Add(1)
    go func(workerID int) {
        defer wg.Done()
        for job := range taskChan {
            // 处理下载任务
            fileData, err := gofile.DownloadAndReadWithRetry(url, maxRetries)
            if job.Task.IsPhoto {
                // 智能图片处理
                processedData, err := gofile.ProcessImageDataWithResize(fileData, gofile.DefaultTargetSizeKB)
            }
        }
    }(i)
}
```

### 智能图片处理
- **大小检测**：只对超过 300KB 的图片进行 resize
- **格式支持**：支持 JPEG、PNG、WebP 格式
- **质量控制**：使用 80% JPEG 质量进行压缩
- **尺寸限制**：最大尺寸限制为 1280 像素

### 并发控制层次
```
Queue Level (batchSize)
├── Queue Worker 1 → ProcessAnalysisResult
│   └── PropProcessor (PropConcurrency workers)
│       ├── Download Worker 1
│       ├── Download Worker 2
│       └── Download Worker N
└── Queue Worker 2 → ProcessAnalysisResult
    └── PropProcessor (PropConcurrency workers)
        ├── Download Worker 1
        ├── Download Worker 2
        └── Download Worker N
```

## 未来改进

1. **内存管理**：添加内存使用监控和限制
2. **部分失败处理**：允许部分文件失败而不影响整个 prop
3. **性能监控**：添加详细的性能指标收集
4. **动态调整**：根据系统负载动态调整并发数
