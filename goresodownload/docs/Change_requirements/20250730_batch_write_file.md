# 需求 [batch_write_file]

## 反馈

1. 写文件是Random Writes

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-28

## 原因

1. 之前是并行下载并行写入，Random Writes

## 解决办法
新的 prop-based 处理架构将每个 prop 的所有变化交给一个 PropProcessor 处理，实现了以下目标：

1. **单一 PropProcessor 处理**：每个 prop 的所有下载、resize 和删除操作由一个 PropProcessor 处理
2. **内部并发下载**：单个 prop 内的多个文件可以并发下载，通过 PropConcurrency 控制
3. **内存缓存**：下载和 resize 后的文件先保存在内存中
4. **批量写盘**：一个 prop 的所有文件都处理完后，一次性写入磁盘
5. **简化架构**：移除了复杂的 WorkerPool 架构，直接使用 PropProcessor


### 1. 实现批量写入缓冲机制
- 在内存中维护一个写入缓冲区，收集多个下载完成的文件数据
- 设置缓冲区大小阈值（如64MB或128MB），达到阈值时触发批量写入
- 设置时间阈值（如5秒），超时后强制写入缓冲区内容

### 2. 顺序写入策略
- 按文件路径或下载顺序对缓冲区内容进行排序
- 使用单独的写入goroutine处理所有文件写入操作
- 避免多个goroutine同时进行随机写入操作

### 3. 文件写入优化
- 使用`os.OpenFile`配合`O_WRONLY|O_CREATE|O_TRUNC`标志
- 对于大文件，使用`bufio.Writer`进行缓冲写入
- 写入完成后立即调用`fsync()`确保数据持久化

### 4. 错误处理和重试机制
- 写入失败时将数据重新加入缓冲区队列
- 实现指数退避重试策略
- 记录写入失败的详细日志信息

### 5. 配置参数
- `batch_write_buffer_size`: 批量写入缓冲区大小（默认64MB）
- `batch_write_timeout`: 批量写入超时时间（默认5秒）
- `max_write_retry`: 最大重试次数（默认3次）

### 6. 不需要实现的功能
- 不需要实现prop_based_processing功能
- 不需要基于文件属性的特殊处理逻辑

## 是否需要补充UT

1. 需要添加批量写入功能的单元测试
2. 验证顺序写入的正确性测试

## 确认日期:    2025-07-28

## online-step

1. 重启goresodownload服务
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"



