# 需求 [batch_write_file]

## 反馈

1. 写文件是Random Writes

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-28

## 原因

1. 之前是并行下载并行写入，Random Writes

## 解决办法
### 1. 接收任务
- `ProcessAnalysisResult` 直接创建 `PropProcessor` 并调用 `Process` 方法

### 2. 单个 Prop 处理 (PropProcessor.Process)
1. **下载和缓存** (`downloadAndCacheFiles`)
   - **固定 Worker 数量**：创建 `PropConcurrency` 个 worker goroutine
   - **任务分发**：通过 channel 将下载任务分发给 workers
   - **智能图片处理**：使用 `gofile.ProcessImageDataWithResize` 对超过 300KB 的图片进行 resize
   - **内存缓存**：将处理后的数据缓存在 `CachedFile` 结构中

2. **生成缩略图** (`generateThumbnail`)
   - 为第一张图片生成缩略图
   - 使用现有的缩略图生成逻辑

3. **批量写盘** (`batchWriteFiles`)
   - 将所有缓存的文件一次性写入所有存储路径
   - 确保目录存在
   - 原子性写入操作

4. **处理删除** (`handleDeletions`)
   - 删除不需要的文件
   - 使用现有的删除逻辑

### 3. 结果处理
- 返回 `PropProcessResult` 包含处理结果
- 更新数据库文档

具体见 docs/prop_based_processing.md 文档

## 是否需要补充UT

1. 需要添加批量写入功能的单元测试
2. 验证顺序写入的正确性测试

## 确认日期:    2025-07-28

## online-step

1. 重启goresodownload服务
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"



