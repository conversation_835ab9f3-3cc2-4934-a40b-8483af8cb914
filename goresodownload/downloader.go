package goresodownload

import (
	"bytes"
	"context"
	"fmt"
	"image/jpeg"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/gofile"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Download processing constants
const (
	// Thumbnail dimensions
	THUMBNAIL_WIDTH  = 240 // Thumbnail width in pixels
	THUMBNAIL_HEIGHT = 160 // Thumbnail height in pixels

	// Error channel buffer size for concurrent processing
	ErrorChannelBufferSize = 2

	// Image compression settings (removed - using gofile.DEFAULT_TARGET_SIZE_KB instead)

	// File system permissions
	DefaultDirPermissions = 0755 // Standard directory permissions (rwxr-xr-x)
)

// CachedFile represents a file cached in memory
type CachedFile struct {
	Path        string // Relative path where the file should be saved
	Data        []byte // File data
	IsPhoto     bool   // Whether this is a photo file
	IsThumbnail bool   // Whether this is a thumbnail file
}

// PropProcessResult represents the result of processing a single prop
type PropProcessResult struct {
	PropID        string
	ThumbnailHash int32
	TnChangedNum  int
	Error         error
}

// downloadTaskJob represents a download task with its index
type downloadTaskJob struct {
	Index int
	Task  MediaTask
}

// PropProcessor handles processing of a single prop's media tasks
type PropProcessor struct {
	downloader  *Downloader
	result      *AnalysisResult
	board       string
	cachedFiles []CachedFile
}

// FailedTask represents a failed download task in the database
type FailedTask struct {
	ID         primitive.ObjectID `bson:"_id,omitempty"`
	PropID     string             `bson:"prop_id"`
	ImageURL   string             `bson:"image_url"`
	ErrMsg     string             `bson:"err_msg"`
	RetryCount int                `bson:"retry_count"`
}

// DownloaderConfig represents the configuration for the downloader
type DownloaderConfig struct {
	// Concurrency settings
	PropConcurrency int `json:"prop_concurrency"` // Number of props to process concurrently

	// Retry settings
	MaxRetries int `json:"max_retries"`

	// Alert settings
	ConsecutiveFailuresThreshold int `json:"consecutive_failures_threshold"` // Default: 3
}

// NewDefaultConfig returns a new DownloaderConfig with default values
func NewDefaultConfig() *DownloaderConfig {
	return &DownloaderConfig{
		PropConcurrency:              3, // Process 3 props concurrently
		MaxRetries:                   3,
		ConsecutiveFailuresThreshold: 3,
	}
}

// DownloaderOptions contains all options for creating a Downloader
type DownloaderOptions struct {
	Config       *DownloaderConfig
	StoragePaths []string
	MergedCol    *gomongo.MongoCollection
	FailedCol    *gomongo.MongoCollection
}



// Downloader implements the media download functionality
type Downloader struct {
	*DownloaderOptions
}


// NewDownloader creates a new Downloader instance with worker pool
func NewDownloader(opts *DownloaderOptions) (*Downloader, error) {
	if opts == nil {
		opts = &DownloaderOptions{
			Config: NewDefaultConfig(),
		}
	}
	if opts.Config == nil {
		opts.Config = NewDefaultConfig()
	}

	// Create downloader first
	downloader := &Downloader{
		DownloaderOptions: opts,
	}

	return downloader, nil
}

// Stop gracefully shuts down the downloader
func (d *Downloader) Stop() {
	// No worker pools to stop in the simplified architecture
}

// ProcessAnalysisResult processes the analysis result directly using PropProcessor
func (d *Downloader) ProcessAnalysisResult(result *AnalysisResult, board string) (tnChangedNum int, err error) {
	golog.Info("Processing analysis result", "propID", result.ID, "downloadTasks", len(result.DownloadTasks), "deleteTasks", len(result.DeleteTasks))

	processor := &PropProcessor{
		downloader:  d,
		result:      result,
		board:       board,
		cachedFiles: make([]CachedFile, 0),
	}

	propResult := processor.Process()
	if propResult.Error != nil {
		return 0, fmt.Errorf("prop processing failed: %w", propResult.Error)
	}

	// Update document with all fields
	golog.Debug("ProcessAnalysisResult", "result", result, "thumbnailHash", propResult.ThumbnailHash)
	if err := d.updateMergedDoc(result, propResult.ThumbnailHash, board); err != nil {
		golog.Error("failed to update merged document", "error", err)
	}

	golog.Info("ProcessAnalysisResult finished", "ID", result.ID, "tnChangedNum", propResult.TnChangedNum)
	return propResult.TnChangedNum, nil
}

// updateMergedDoc updates the merged document with PhoLH, DocLH and optional thumbnail hash
func (d *Downloader) updateMergedDoc(result *AnalysisResult, thumbnailHash int32, board string) error {
	if result == nil {
		golog.Error("updateMergedDoc", "result", result)
		return fmt.Errorf("result is nil")
	}

	// Check if MergedCol is available
	if d.MergedCol == nil {
		golog.Warn("MergedCol is nil, skipping document update", "ID", result.ID)
		return nil // Don't fail the entire process if MergedCol is not available
	}

	// Check if document exists
	golog.Info("Checking if document exists in merged collection", "ID", result.ID, "collection", d.MergedCol.Name())
	var doc bson.M
	err := d.MergedCol.FindOne(context.Background(), bson.M{"_id": result.ID}).Decode(&doc)
	if err != nil {
		golog.Error("updateMergedDoc - document not found", "error", err, "ID", result.ID, "collection", d.MergedCol.Name())
		return fmt.Errorf("document not found: %w", err)
	}
	golog.Debug("Document found in merged collection", "ID", result.ID)

	update := bson.M{}
	update["$set"] = bson.M{}
	unsetFields := bson.M{}

	golog.Debug("updateMergedDoc", "result", result.DocLH, "thumbnailHash", thumbnailHash, "board", board)

	// Handle PhoLH
	if len(result.PhoLH) > 0 {
		update["$set"].(bson.M)["phoLH"] = result.PhoLH
	} else {
		unsetFields["phoLH"] = ""
	}

	// Handle DocLH
	if len(result.DocLH) > 0 {
		update["$set"].(bson.M)["docLH"] = result.DocLH
	} else {
		unsetFields["docLH"] = ""
	}

	// Add thumbnail hash if available
	if thumbnailHash != 0 {
		update["$set"].(bson.M)["tnLH"] = thumbnailHash
	} else {
		unsetFields["tnLH"] = ""
	}

	// add phoP:'/L1/L2'
	// 先检查是否已经有phoP字段，如果有则跳过生成
	if !shouldSkipPhoPGeneration(doc) {
		filePath, err := levelStore.GetFullFilePathForProp(result.PropTs, board, result.Sid)
		if err != nil {
			return fmt.Errorf("failed to get full file path for sid %s: %w", result.Sid, err)
		}
		if filePath != "" {
			update["$set"].(bson.M)["phoP"] = filePath
		} else {
			unsetFields["phoP"] = ""
		}
	}

	// 添加图片下载统计字段
	// TODO: 待collectPhotoStats修改后再添加
	// if result.MaxPicSz > 0 {
	// 	update["$set"].(bson.M)["maxPicSz"] = result.MaxPicSz
	// } else {
	// 	unsetFields["maxPicSz"] = ""
	// }

	// if result.AvgPicSz > 0 {
	// 	update["$set"].(bson.M)["avgPicSz"] = result.AvgPicSz
	// } else {
	// 	unsetFields["avgPicSz"] = ""
	// }

	// if result.TotPicSz > 0 {
	// 	update["$set"].(bson.M)["totPicSz"] = result.TotPicSz
	// } else {
	// 	unsetFields["totPicSz"] = ""
	// }

	// if result.PhodlNum > 0 {
	// 	update["$set"].(bson.M)["phodlNum"] = result.PhodlNum
	// } else {
	// 	unsetFields["phodlNum"] = ""
	// }

	// if !result.PhoDl.IsZero() {
	// 	update["$set"].(bson.M)["phoDl"] = result.PhoDl
	// } else {
	// 	unsetFields["phoDl"] = ""
	// }

	// Only add $unset to update if there are fields to unset
	if len(unsetFields) > 0 {
		update["$unset"] = unsetFields
	}

	// Check if there's anything to update
	setFields := update["$set"].(bson.M)
	if len(setFields) == 0 && len(unsetFields) == 0 {
		golog.Debug("updateMergedDoc", "no fields to update")
		return nil
	}

	golog.Debug("updateMergedDoc", "set", update["$set"], "unset", update["$unset"])

	// Only for test
	// update["$set"].(bson.M)["DownloadTasks"] = result.DownloadTasks
	// update["$set"].(bson.M)["DeleteTasks"] = result.DeleteTasks

	updateResult, err := d.MergedCol.UpdateOne(
		context.Background(),
		bson.M{"_id": result.ID},
		update,
	)
	if err != nil {
		golog.Error("Failed to update merged document", "error", err, "ID", result.ID)
		return err
	}

	golog.Debug("Successfully updated merged document",
		"ID", result.ID,
		"matchedCount", updateResult.MatchedCount,
		"modifiedCount", updateResult.ModifiedCount)
	return nil
}



// downloadTask handles a single download task
func (d *Downloader) downloadTask(task MediaTask, id string) error {
	// Prepare full paths for all storage locations
	var fullPaths []string
	for _, basePath := range d.StoragePaths {
		fullPath := filepath.Join(basePath, task.DestPath)
		fullPaths = append(fullPaths, fullPath)
	}

	// Ensure URL has proper protocol prefix
	url := task.URL
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}

	golog.Debug("downloading file", "url", url, "savePaths", fullPaths)
	opts := &gofile.DownloadAndSaveFileOptions{
		URL:            url,
		SavePaths:      fullPaths,
		CompressWebP:   false,
		IsPhoto:        task.IsPhoto,
		MaxRetries:     d.Config.MaxRetries,
		CompressToSize: true,
		TargetSizeKB:   gofile.DEFAULT_TARGET_SIZE_KB,
	}

	// Download and save the file using gofile's internal retry and timeout mechanisms
	golog.Debug("downloadTask", "opts", opts)
	_, err := gofile.DownloadAndSaveFile(opts)
	if err != nil {
		// Clean up any partial files that might have been created
		d.cleanupPartialDownloads(fullPaths)

		// Record failed task
		if recordErr := d.recordFailedTask(task, id, err, d.Config.MaxRetries); recordErr != nil {
			golog.Error("failed to record failed task", "error", recordErr)
		}
		return fmt.Errorf("download failed: %w", err)
	}

	golog.Debug("downloadTask", "success", opts.URL, "savePaths", fullPaths)
	return nil
}

// cleanupPartialDownloads removes any partial files that might have been created during failed downloads
func (d *Downloader) cleanupPartialDownloads(paths []string) {
	for _, path := range paths {
		// Check if file exists and remove it
		if _, err := os.Stat(path); err == nil {
			if err := os.Remove(path); err != nil {
				golog.Error("failed to cleanup partial file", "path", path, "error", err)
			} else {
				golog.Info("cleaned up partial file", "path", path)
			}
		}
	}
}

// deleteTask handles a single deletion task
func (d *Downloader) deleteTask(task DeleteTask) error {
	for _, basePath := range d.StoragePaths {
		fullPath := filepath.Join(basePath, task.Path)

		// Check if path exists and is a file
		fileInfo, err := os.Stat(fullPath)
		if err != nil {
			if os.IsNotExist(err) {
				golog.Info("file does not exist, skipping deletion", "path", fullPath)
				continue
			}
			golog.Error("failed to stat file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to stat file %s: %w", fullPath, err)
		}

		// Skip if it's a directory
		if fileInfo.IsDir() {
			golog.Info("path is a directory, skipping deletion", "path", fullPath)
			continue
		}

		// Delete the file
		if err := os.Remove(fullPath); err != nil {
			golog.Error("failed to delete file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to delete file %s: %w", fullPath, err)
		}
	}
	return nil
}

// recordFailedTask records a failed download task in the database
func (d *Downloader) recordFailedTask(task MediaTask, id string, err error, retryCount int) error {
	failedTask := FailedTask{
		PropID:     id,
		ImageURL:   task.URL,
		ErrMsg:     err.Error(),
		RetryCount: retryCount,
	}
	if d.FailedCol == nil {
		return fmt.Errorf("FailedCol is nil, cannot record failed task")
	}
	_, err = d.FailedCol.InsertOne(context.Background(), failedTask)
	return err
}


// collectPhotoStats 收集图片下载统计信息
func (d *Downloader) collectPhotoStats(result *AnalysisResult) error {
	if len(result.DownloadTasks) == 0 {
		golog.Debug("No download tasks, skipping photo stats collection")
		return nil
	}

	var totalSize int64
	var maxSize int64
	var photoCount int

	// 筛选出图片任务并收集文件大小信息
	for _, task := range result.DownloadTasks {
		if !task.IsPhoto {
			continue // 跳过非图片文件
		}

		// 获取第一个存储路径的文件大小（所有路径的文件应该相同）
		if len(d.StoragePaths) > 0 {
			fullPath := filepath.Join(d.StoragePaths[0], task.DestPath)
			if fileInfo, err := os.Stat(fullPath); err == nil {
				fileSize := fileInfo.Size()
				totalSize += fileSize
				if fileSize > maxSize {
					maxSize = fileSize
				}
				photoCount++
				golog.Debug("Photo file stats collected",
					"path", fullPath,
					"size", fileSize,
					"mediaKey", task.MediaKey)
			} else {
				golog.Warn("Failed to get file stats",
					"path", fullPath,
					"error", err,
					"mediaKey", task.MediaKey)
			}
		}
	}

	// 计算统计信息
	if photoCount > 0 {
		result.MaxPicSz = maxSize
		result.TotPicSz = totalSize
		result.AvgPicSz = totalSize / int64(photoCount)
		result.PhodlNum = photoCount
		result.PhoDl = time.Now() // 下载完成时间戳

		golog.Info("Photo download stats collected",
			"ID", result.ID,
			"photoCount", photoCount,
			"maxSize", maxSize,
			"totalSize", totalSize,
			"avgSize", result.AvgPicSz,
			"downloadTime", result.PhoDl)
	} else {
		golog.Debug("No photo files found for stats collection", "ID", result.ID)
	}

	return nil
}

// PropProcessor methods

// Process handles all tasks for a single prop
func (pp *PropProcessor) Process() PropProcessResult {
	golog.Info("Processing prop", "propID", pp.result.ID, "downloadTasks", len(pp.result.DownloadTasks), "deleteTasks", len(pp.result.DeleteTasks))

	// Step 1: Download and cache all files in memory
	if err := pp.downloadAndCacheFiles(); err != nil {
		return PropProcessResult{
			PropID: pp.result.ID,
			Error:  fmt.Errorf("failed to download and cache files: %w", err),
		}
	}

	// Step 2: Generate thumbnail if needed
	thumbnailHash, tnChangedNum, err := pp.generateThumbnail()
	if err != nil {
		golog.Error("Failed to generate thumbnail", "propID", pp.result.ID, "error", err)
		// Don't fail the entire process for thumbnail errors
	}

	// Step 3: Batch write all files to disk
	if err := pp.batchWriteFiles(); err != nil {
		return PropProcessResult{
			PropID: pp.result.ID,
			Error:  fmt.Errorf("failed to batch write files: %w", err),
		}
	}

	// Step 4: Handle deletions
	if err := pp.handleDeletions(); err != nil {
		golog.Error("Failed to handle deletions", "propID", pp.result.ID, "error", err)
		// Don't fail the entire process for deletion errors
	}

	golog.Info("Successfully processed prop", "propID", pp.result.ID, "cachedFiles", len(pp.cachedFiles))

	return PropProcessResult{
		PropID:        pp.result.ID,
		ThumbnailHash: thumbnailHash,
		TnChangedNum:  tnChangedNum,
		Error:         nil,
	}
}

// downloadAndCacheFiles downloads all files and caches them in memory using fixed number of workers
func (pp *PropProcessor) downloadAndCacheFiles() error {
	if len(pp.result.DownloadTasks) == 0 {
		golog.Info("No download tasks for prop", "propID", pp.result.ID)
		return nil
	}

	golog.Info("Downloading and caching files with worker pool", "propID", pp.result.ID, "taskCount", len(pp.result.DownloadTasks), "workers", pp.downloader.Config.PropConcurrency)

	// Create task channel and result storage
	taskChan := make(chan downloadTaskJob, len(pp.result.DownloadTasks))
	var wg sync.WaitGroup
	var mu sync.Mutex
	var firstErr error
	cachedFiles := make([]CachedFile, len(pp.result.DownloadTasks))

	// Start fixed number of worker goroutines
	numWorkers := pp.downloader.Config.PropConcurrency
	if numWorkers > len(pp.result.DownloadTasks) {
		numWorkers = len(pp.result.DownloadTasks) // Don't create more workers than tasks
	}

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			golog.Debug("Download worker started", "propID", pp.result.ID, "workerID", workerID)

			for job := range taskChan {
				golog.Debug("Processing download task", "propID", pp.result.ID, "workerID", workerID, "taskIndex", job.Index, "url", job.Task.URL)

				// Ensure URL has proper protocol prefix
				url := job.Task.URL
				if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
					url = "https://" + url
				}

				// Download file using gofile's function
				fileData, err := gofile.DownloadAndReadWithRetry(url, pp.downloader.Config.MaxRetries)
				if err != nil {
					// Record failed task
					if recordErr := pp.downloader.recordFailedTask(job.Task, pp.result.ID, err, pp.downloader.Config.MaxRetries); recordErr != nil {
						golog.Error("Failed to record failed task", "error", recordErr)
					}

					mu.Lock()
					if firstErr == nil {
						firstErr = fmt.Errorf("failed to download file %s: %w", url, err)
					}
					mu.Unlock()
					continue
				}

				// If it's a photo, process and resize if needed
				if job.Task.IsPhoto {
					processedData, err := gofile.ProcessImageDataWithResize(fileData) // Uses DEFAULT_TARGET_SIZE_KB
					if err != nil {
						golog.Error("Failed to process image", "propID", pp.result.ID, "url", url, "error", err)
						// Use original data if processing fails
						processedData = fileData
					}
					fileData = processedData
				}

				// Cache the file (use index to maintain order)
				cachedFile := CachedFile{
					Path:    job.Task.DestPath,
					Data:    fileData,
					IsPhoto: job.Task.IsPhoto,
				}

				mu.Lock()
				cachedFiles[job.Index] = cachedFile
				mu.Unlock()

				golog.Debug("Successfully cached file", "propID", pp.result.ID, "workerID", workerID, "taskIndex", job.Index, "size", len(fileData))
			}

			golog.Debug("Download worker finished", "propID", pp.result.ID, "workerID", workerID)
		}(i)
	}

	// Send all tasks to workers
	for i, task := range pp.result.DownloadTasks {
		taskChan <- downloadTaskJob{
			Index: i,
			Task:  task,
		}
	}
	close(taskChan)

	// Wait for all workers to finish
	wg.Wait()

	// Check for errors
	if firstErr != nil {
		return firstErr
	}

	// Copy results to pp.cachedFiles
	pp.cachedFiles = cachedFiles

	golog.Info("Successfully downloaded and cached all files", "propID", pp.result.ID, "fileCount", len(pp.cachedFiles))
	return nil
}

// generateThumbnail generates thumbnail for the first photo and caches it
func (pp *PropProcessor) generateThumbnail() (int32, int, error) {
	if pp.result.NewFirstPic.MediaKey == "" {
		return 0, 0, nil
	}

	// Generate thumbnail and cache it instead of writing directly to disk
	return pp.generateThumbnailToCache(pp.result.NewFirstPic, pp.result.OldTnLH)
}

// generateThumbnailToCache generates thumbnail and adds it to cache for batch writing
func (pp *PropProcessor) generateThumbnailToCache(task MediaTask, oldTnLH int32) (int32, int, error) {
	var tnChangedNum int
	// Create thumbnail key
	thumbKey := task.MediaKey + "-t"
	sid := task.Sid

	// Generate hash and filename
	hash := levelStore.MurmurToInt32(thumbKey)
	golog.Debug("generateThumbnailToCache", "hash", hash, "oldTnLH", oldTnLH)
	if hash == oldTnLH { // thumbnail not changed
		golog.Info("thumbnail already exists, skipping", "hash", hash)
		return hash, 0, nil
	}
	if oldTnLH != 0 && hash == 0 {
		tnChangedNum = -1
	}
	if oldTnLH == 0 && hash != 0 {
		tnChangedNum = 1
	}

	fileName, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to generate thumbnail filename: %w", err)
	}

	// Download and resize image
	newImg, err := gofile.DownloadAndResizeImage(task.URL, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to download and resize image: %w", err)
	}

	// Encode thumbnail to bytes instead of writing to file
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, newImg, &jpeg.Options{Quality: 100})
	if err != nil {
		return 0, 0, fmt.Errorf("failed to encode thumbnail: %w", err)
	}
	thumbnailData := buf.Bytes()

	// Get the directory of the original file for thumbnail path
	dirPath := filepath.Dir(task.DestPath)
	thumbnailPath := filepath.Join(dirPath, sid+"_"+fileName+".jpg")

	// Add thumbnail to cache
	thumbnailFile := CachedFile{
		Path:        thumbnailPath,
		Data:        thumbnailData,
		IsPhoto:     false, // Thumbnail is not considered a regular photo
		IsThumbnail: true,
	}
	pp.cachedFiles = append(pp.cachedFiles, thumbnailFile)

	// Add old thumbnail to DeleteTasks for unified deletion handling
	if oldTnLH != 0 {
		fileNameOld, err := levelStore.Int32ToBase62(oldTnLH)
		if err != nil {
			golog.Error("failed to generate old thumbnail filename", "error", err, "oldTnLH", oldTnLH)
		} else {
			oldThumbnailPath := filepath.Join(dirPath, sid+"_"+fileNameOld+".jpg")

			// Create delete task for old thumbnail
			deleteTask := DeleteTask{
				Sid:      sid,
				MediaKey: "", // MediaKey is not needed for deletion operation
				Path:     oldThumbnailPath,
			}

			// Add to DeleteTasks for unified deletion handling
			pp.result.DeleteTasks = append(pp.result.DeleteTasks, deleteTask)
			golog.Debug("Old thumbnail added to DeleteTasks", "path", oldThumbnailPath, "oldTnLH", oldTnLH)
		}
	}

	golog.Debug("Thumbnail cached for batch writing", "hash", hash, "fileName", fileName, "path", thumbnailPath, "size", len(thumbnailData))
	return hash, tnChangedNum, nil
}

// batchWriteFiles writes all cached files to disk in one operation
func (pp *PropProcessor) batchWriteFiles() error {
	if len(pp.cachedFiles) == 0 {
		golog.Info("No files to write", "propID", pp.result.ID)
		return nil
	}

	golog.Info("Batch writing files to disk", "propID", pp.result.ID, "fileCount", len(pp.cachedFiles))

	// Write all files to all storage paths
	for _, cachedFile := range pp.cachedFiles {
		for _, basePath := range pp.downloader.StoragePaths {
			fullPath := filepath.Join(basePath, cachedFile.Path)

			// Create directory if it doesn't exist
			dir := filepath.Dir(fullPath)
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", dir, err)
			}

			// Write file
			if err := os.WriteFile(fullPath, cachedFile.Data, 0644); err != nil {
				return fmt.Errorf("failed to write file %s: %w", fullPath, err)
			}

			fileType := "file"
			if cachedFile.IsPhoto {
				fileType = "photo"
			} else if cachedFile.IsThumbnail {
				fileType = "thumbnail"
			}

			golog.Debug("Successfully wrote file", "propID", pp.result.ID, "path", fullPath, "size", len(cachedFile.Data), "type", fileType)
		}
	}

	golog.Info("Successfully batch wrote all files", "propID", pp.result.ID, "fileCount", len(pp.cachedFiles))
	return nil
}

// handleDeletions handles file deletions
func (pp *PropProcessor) handleDeletions() error {
	if len(pp.result.DeleteTasks) == 0 {
		golog.Info("No deletion tasks for prop", "propID", pp.result.ID)
		return nil
	}

	golog.Info("Handling deletions", "propID", pp.result.ID, "taskCount", len(pp.result.DeleteTasks))

	for _, task := range pp.result.DeleteTasks {
		if err := pp.downloader.deleteTask(task); err != nil {
			golog.Error("Failed to delete file", "propID", pp.result.ID, "path", task.Path, "error", err)
			// Continue with other deletions even if one fails
		}
	}

	golog.Info("Successfully handled all deletions", "propID", pp.result.ID, "taskCount", len(pp.result.DeleteTasks))
	return nil
}
