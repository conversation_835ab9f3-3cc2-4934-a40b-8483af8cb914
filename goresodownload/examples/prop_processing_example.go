package main

import (
	"fmt"
	"log"
	"time"

	"github.com/real-rm/goresodownload"
)

func main() {
	// Create configuration
	config := goresodownload.NewDefaultConfig()
	config.PropConcurrency = 2 // Process 2 props concurrently
	config.MaxRetries = 3       // Retry failed downloads up to 3 times

	// Create downloader options
	opts := &goresodownload.DownloaderOptions{
		Config:       config,
		StoragePaths: []string{"/tmp/downloads"}, // Storage paths
		// MergedCol and FailedCol would be set in real usage
	}

	// Create downloader
	downloader, err := goresodownload.NewDownloader(opts)
	if err != nil {
		log.Fatalf("Failed to create downloader: %v", err)
	}
	defer downloader.Stop()

	// Create example analysis result with multiple files to demonstrate concurrency
	result := &goresodownload.AnalysisResult{
		ID:  "example_prop_123",
		Sid: "example_sid",
		DownloadTasks: []goresodownload.MediaTask{
			{
				Sid:      "example_sid",
				MediaKey: "photo_1",
				URL:      "https://httpbin.org/image/jpeg", // Test image URL
				Type:     "photo",
				DestPath: "example/photo1.jpg",
				IsPhoto:  true,
			},
			{
				Sid:      "example_sid",
				MediaKey: "photo_2",
				URL:      "https://httpbin.org/image/png", // Another test image URL
				Type:     "photo",
				DestPath: "example/photo2.png",
				IsPhoto:  true,
			},
			{
				Sid:      "example_sid",
				MediaKey: "doc_1",
				URL:      "https://httpbin.org/json", // Test document URL
				Type:     "document",
				DestPath: "example/document.json",
				IsPhoto:  false,
			},
		},
		DeleteTasks: []goresodownload.DeleteTask{
			{
				Sid:  "example_sid",
				Path: "example/old_photo.jpg",
			},
		},
		PhoLH:  []int32{12345, 67890},
		DocLH:  []string{},
		PropTs: time.Now(),
		NewFirstPic: goresodownload.MediaTask{
			Sid:      "example_sid",
			MediaKey: "photo_1",
			URL:      "https://example.com/photo1.jpg",
			Type:     "photo",
			DestPath: "example/photo1.jpg",
			IsPhoto:  true,
		},
	}

	// Process the analysis result using the new prop-based approach
	fmt.Println("Processing analysis result with prop-based architecture...")
	tnChangedNum, err := downloader.ProcessAnalysisResult(result, "EXAMPLE")
	if err != nil {
		log.Printf("Processing failed: %v", err)
	} else {
		fmt.Printf("Processing completed successfully! Thumbnail changed: %d\n", tnChangedNum)
	}

	fmt.Println("Example completed.")
}
